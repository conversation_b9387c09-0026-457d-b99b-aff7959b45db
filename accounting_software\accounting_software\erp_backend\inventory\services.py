"""
Inventory Service Layer for ERP System

This module provides business logic for inventory operations including:
- Real-time inventory availability checking
- Automatic inventory reservation and issuing
- Integration with invoice creation
- Warehouse-specific inventory management
"""

from django.db import transaction, connection
from django.utils import timezone
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
import json

from .models import Inventory, StockTransaction, Warehouse
from sales.models import Product


class InventoryService:
    """Service class for inventory operations"""
    
    @staticmethod
    def check_availability(product_id: int, warehouse_id: int, quantity: Decimal) -> Dict:
        """
        Check if sufficient inventory is available for a product in a warehouse
        
        Args:
            product_id: ID of the product
            warehouse_id: ID of the warehouse
            quantity: Required quantity
            
        Returns:
            Dict with availability information
        """
        try:
            inventory = Inventory.objects.get(
                product_id=product_id, 
                warehouse_id=warehouse_id
            )
            
            available = inventory.available_quantity
            
            return {
                'success': True,
                'available': float(available),
                'on_hand': float(inventory.quantity_on_hand),
                'reserved': float(inventory.quantity_reserved),
                'requested': float(quantity),
                'sufficient': available >= quantity,
                'stock_status': InventoryService._get_stock_status(inventory),
                'reorder_point': float(inventory.reorder_point),
                'average_cost': float(inventory.average_cost)
            }
            
        except Inventory.DoesNotExist:
            return {
                'success': False,
                'error': 'Product not found in warehouse',
                'available': 0,
                'requested': float(quantity),
                'sufficient': False,
                'stock_status': 'NOT_FOUND'
            }
    
    @staticmethod
    def check_multiple_availability(items: List[Dict]) -> Dict:
        """
        Check availability for multiple products
        
        Args:
            items: List of dicts with product_id, warehouse_id, quantity
            
        Returns:
            Dict with overall availability status and individual item details
        """
        results = []
        all_available = True
        issues = []
        
        for item in items:
            result = InventoryService.check_availability(
                item['product_id'],
                item['warehouse_id'], 
                Decimal(str(item['quantity']))
            )
            
            results.append({
                **result,
                'product_id': item['product_id'],
                'warehouse_id': item['warehouse_id']
            })
            
            if not result['sufficient']:
                all_available = False
                issues.append({
                    'product_id': item['product_id'],
                    'warehouse_id': item['warehouse_id'],
                    'issue': result.get('error', 'Insufficient stock'),
                    'available': result['available'],
                    'requested': result['requested']
                })
        
        return {
            'success': True,
            'all_available': all_available,
            'items': results,
            'issues': issues,
            'total_items': len(items),
            'available_items': len([r for r in results if r['sufficient']])
        }
    
    @staticmethod
    @transaction.atomic
    def reserve_inventory(product_id: int, warehouse_id: int, quantity: Decimal, 
                         reference_type: str, reference_id: int, user_id: int = None) -> Dict:
        """
        Reserve inventory for a future transaction (like sales order)
        
        Args:
            product_id: ID of the product
            warehouse_id: ID of the warehouse  
            quantity: Quantity to reserve
            reference_type: Type of document (SO, INV, etc.)
            reference_id: ID of the reference document
            user_id: ID of the user making the reservation
            
        Returns:
            Dict with reservation result
        """
        try:
            inventory = Inventory.objects.select_for_update().get(
                product_id=product_id,
                warehouse_id=warehouse_id
            )
            
            available = inventory.available_quantity
            
            if available < quantity:
                return {
                    'success': False,
                    'error': 'Insufficient stock for reservation',
                    'available': float(available),
                    'requested': float(quantity)
                }
            
            # Update reservation
            inventory.quantity_reserved += quantity
            inventory.last_updated = timezone.now()
            inventory.save()
            
            # Create stock transaction
            StockTransaction.objects.create(
                product_id=product_id,
                warehouse_id=warehouse_id,
                transaction_type='RESERVATION',
                reference_type=reference_type,
                reference_id=reference_id,
                quantity=quantity,
                unit_cost=Decimal('0.00'),
                total_cost=Decimal('0.00'),
                txn_date=timezone.now(),
                created_by_id=user_id,
                notes=f'Reserved for {reference_type}-{reference_id}'
            )
            
            return {
                'success': True,
                'reserved': float(quantity),
                'remaining_available': float(available - quantity),
                'total_reserved': float(inventory.quantity_reserved)
            }
            
        except Inventory.DoesNotExist:
            return {
                'success': False,
                'error': 'Product not found in warehouse'
            }
    
    @staticmethod
    @transaction.atomic
    def issue_inventory(product_id: int, warehouse_id: int, quantity: Decimal,
                       reference_type: str, reference_id: int, unit_cost: Decimal = None,
                       user_id: int = None) -> Dict:
        """
        Issue inventory (reduce stock) for a transaction like invoice
        
        Args:
            product_id: ID of the product
            warehouse_id: ID of the warehouse
            quantity: Quantity to issue
            reference_type: Type of document (INV, SO, etc.)
            reference_id: ID of the reference document
            unit_cost: Cost per unit for this transaction
            user_id: ID of the user making the transaction
            
        Returns:
            Dict with issue result
        """
        try:
            inventory = Inventory.objects.select_for_update().get(
                product_id=product_id,
                warehouse_id=warehouse_id
            )
            
            if inventory.quantity_on_hand < quantity:
                return {
                    'success': False,
                    'error': 'Insufficient stock for issue',
                    'on_hand': float(inventory.quantity_on_hand),
                    'requested': float(quantity)
                }
            
            # Use average cost if unit cost not provided
            if unit_cost is None:
                unit_cost = inventory.average_cost
            
            # Update inventory quantities
            inventory.quantity_on_hand -= quantity
            inventory.quantity_reserved = max(Decimal('0'), inventory.quantity_reserved - quantity)
            inventory.last_transaction_date = timezone.now()
            inventory.last_updated = timezone.now()
            inventory.save()
            
            # Create stock transaction
            StockTransaction.objects.create(
                product_id=product_id,
                warehouse_id=warehouse_id,
                transaction_type='ISSUE',
                reference_type=reference_type,
                reference_id=reference_id,
                quantity=-quantity,  # Negative for outgoing
                unit_cost=unit_cost,
                total_cost=-(quantity * unit_cost),
                txn_date=timezone.now(),
                created_by_id=user_id,
                notes=f'Issued for {reference_type}-{reference_id}'
            )
            
            return {
                'success': True,
                'issued': float(quantity),
                'remaining_on_hand': float(inventory.quantity_on_hand),
                'unit_cost': float(unit_cost),
                'total_cost': float(quantity * unit_cost)
            }
            
        except Inventory.DoesNotExist:
            return {
                'success': False,
                'error': 'Product not found in warehouse'
            }
    
    @staticmethod
    def get_warehouse_inventory(warehouse_id: int, product_id: int = None) -> List[Dict]:
        """
        Get inventory levels for a warehouse
        
        Args:
            warehouse_id: ID of the warehouse
            product_id: Optional product ID to filter
            
        Returns:
            List of inventory items with availability info
        """
        queryset = Inventory.objects.filter(warehouse_id=warehouse_id)
        
        if product_id:
            queryset = queryset.filter(product_id=product_id)
        
        queryset = queryset.select_related('product', 'warehouse')
        
        results = []
        for inventory in queryset:
            results.append({
                'inventory_id': inventory.inventory_id,
                'product_id': inventory.product.id,
                'product_name': inventory.product.name,
                'product_sku': getattr(inventory.product, 'sku', ''),
                'warehouse_id': inventory.warehouse.warehouse_id,
                'warehouse_name': inventory.warehouse.name,
                'quantity_on_hand': float(inventory.quantity_on_hand),
                'quantity_reserved': float(inventory.quantity_reserved),
                'available_quantity': float(inventory.available_quantity),
                'reorder_point': float(inventory.reorder_point),
                'average_cost': float(inventory.average_cost),
                'last_cost': float(inventory.last_cost),
                'stock_status': InventoryService._get_stock_status(inventory),
                'last_transaction_date': inventory.last_transaction_date
            })
        
        return results
    
    @staticmethod
    def _get_stock_status(inventory: Inventory) -> str:
        """Get stock status for an inventory item"""
        available = inventory.available_quantity
        
        if available <= 0:
            return 'OUT_OF_STOCK'
        elif available <= inventory.reorder_point:
            return 'LOW_STOCK'
        else:
            return 'IN_STOCK'
    
    @staticmethod
    def get_low_stock_items(warehouse_id: int = None) -> List[Dict]:
        """Get items that are low on stock or out of stock"""
        from django.db.models import F
        
        queryset = Inventory.objects.filter(
            quantity_on_hand__lte=F('reorder_point')
        ).select_related('product', 'warehouse')
        
        if warehouse_id:
            queryset = queryset.filter(warehouse_id=warehouse_id)
        
        results = []
        for inventory in queryset:
            results.append({
                'inventory_id': inventory.inventory_id,
                'product_id': inventory.product.id,
                'product_name': inventory.product.name,
                'warehouse_name': inventory.warehouse.name,
                'quantity_on_hand': float(inventory.quantity_on_hand),
                'reorder_point': float(inventory.reorder_point),
                'stock_status': InventoryService._get_stock_status(inventory),
                'shortage': float(max(0, inventory.reorder_point - inventory.quantity_on_hand))
            })
        
        return results


class InvoiceInventoryService:
    """Service class specifically for invoice-inventory integration"""

    @staticmethod
    @transaction.atomic
    def process_invoice_inventory(invoice, user_id=None):
        """
        Process inventory for an invoice - check availability and issue stock

        Args:
            invoice: Invoice instance
            user_id: ID of the user processing the invoice

        Returns:
            Dict with processing result
        """
        # First check if inventory is available
        availability_result = invoice.check_inventory_availability()

        if not availability_result['success']:
            return availability_result

        if not availability_result.get('all_available', False):
            return {
                'success': False,
                'error': 'Insufficient inventory for invoice',
                'availability_details': availability_result
            }

        # Issue the inventory
        issue_result = invoice.issue_inventory(user_id)

        return issue_result

    @staticmethod
    def get_invoice_inventory_status(invoice):
        """Get detailed inventory status for an invoice"""
        if not invoice.warehouse:
            return {
                'success': False,
                'error': 'No warehouse selected'
            }

        line_items_status = []
        overall_status = 'available'

        for line_item in invoice.line_items.all():
            if line_item.product and line_item.product.type == 'product':
                availability = InventoryService.check_availability(
                    line_item.product.id,
                    invoice.warehouse.warehouse_id,
                    line_item.quantity
                )

                line_status = {
                    'line_item_id': line_item.id,
                    'product_id': line_item.product.id,
                    'product_name': line_item.product.name,
                    'requested_quantity': float(line_item.quantity),
                    'available_quantity': availability['available'],
                    'sufficient': availability['sufficient'],
                    'stock_status': availability['stock_status'],
                    'inventory_issued': line_item.inventory_issued
                }

                if not availability['sufficient']:
                    overall_status = 'insufficient'
                elif availability['stock_status'] == 'LOW_STOCK':
                    if overall_status == 'available':
                        overall_status = 'low_stock'

                line_items_status.append(line_status)

        return {
            'success': True,
            'overall_status': overall_status,
            'inventory_issued': invoice.inventory_issued,
            'line_items': line_items_status,
            'warehouse': {
                'id': invoice.warehouse.warehouse_id,
                'name': invoice.warehouse.name
            }
        }
