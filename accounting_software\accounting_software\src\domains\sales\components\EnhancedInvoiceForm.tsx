import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Grid,
  Typography,
  TextField,
  Autocomplete,
  Button,
  Divider,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Save as SaveIcon,
  Send as SendIcon,
  Print as PrintIcon,
  Add as AddIcon,
  Clear as ClearIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';

// Import shared components
import {
  JournalLineTable,
  type JournalLineItem,
  FormattedCurrencyInput,
  QuantityInput,
} from '../../../shared/components';

// Import services
import { customerService, type CustomerOption } from '../../../services/customer.service';
import { salesTaxService, type SalesTaxOption } from '../../../services/sales-tax.service';
import { invoiceService } from '../../../services/invoice.service';

// Types
interface InvoiceCustomer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
}

interface InvoiceProduct {
  id: number;
  name: string;
  description?: string;
  unit_price: number;
  type: 'product' | 'service';
  tax_category?: string;
}

interface InvoiceLineItem extends JournalLineItem {
  product_id: number | null;
  product_name: string;
  description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  sales_tax_id: number | null;
  sales_tax_rate: number;
  sales_tax_amount: number;
  account_name?: string; // For display in table
}

interface InvoiceFormData {
  customer_id: number | null;
  invoice_date: string;
  due_date: string;
  invoice_number?: string;
  po_number?: string;
  terms: string;
  notes: string;
  line_items: InvoiceLineItem[];
  subtotal: number;
  total_tax: number;
  total_amount: number;
  balance_due: number;
}

interface EnhancedInvoiceFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  invoice?: any;
  fullPage?: boolean;
}

const EnhancedInvoiceForm: React.FC<EnhancedInvoiceFormProps> = ({
  onSuccess,
  onCancel,
  invoice,
  fullPage = false,
}) => {
  // State
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<InvoiceCustomer[]>([]);
  const [products, setProducts] = useState<InvoiceProduct[]>([]);
  const [outputTaxes, setOutputTaxes] = useState<SalesTaxOption[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<InvoiceCustomer | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<InvoiceFormData>({
    customer_id: null,
    invoice_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
    po_number: '',
    terms: 'Net 30',
    notes: '',
    line_items: [],
    subtotal: 0,
    total_tax: 0,
    total_amount: 0,
    balance_due: 0,
  });

  // Snackbar state
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load data on component mount
  useEffect(() => {
    console.log('🚀 Loading invoice form data...');
    loadCustomers();
    loadProducts();
    loadOutputTaxes();
  }, []);

  // Add initial line item if none exist
  useEffect(() => {
    if (formData.line_items.length === 0) {
      const initialLine: InvoiceLineItem = {
        id: `line_${Date.now()}_initial`,
        account_id: null,
        account_name: '',
        description: '',
        memo: '',
        amount: 0,
        product_id: null,
        product_name: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        sales_tax_id: null,
        sales_tax_rate: 0,
        sales_tax_amount: 0,
      };

      setFormData(prev => ({
        ...prev,
        line_items: [initialLine],
      }));
    }
  }, [formData.line_items.length]);

  const loadCustomers = async () => {
    console.log('📞 Loading customers...');
    try {
      const token = localStorage.getItem('token');
      console.log('🔑 Token exists:', !!token);

      // Use the contacts API endpoint for customers
      const response = await fetch('http://localhost:8000/api/contacts/customers/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('📡 Customer API response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Loaded customers:', data);
        const customerList = data.results || [];
        console.log('👥 Customer count:', customerList.length);
        setCustomers(customerList);
      } else {
        const errorText = await response.text();
        console.error('❌ Customer API error:', errorText);
        throw new Error(`Failed to load customers: ${response.status}`);
      }
    } catch (error) {
      console.error('💥 Error loading customers:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load customers',
        severity: 'error',
      });
    }
  };

  const loadProducts = async () => {
    try {
      // This would be replaced with actual product service call
      const response = await fetch('http://localhost:8000/api/sales/products/', {
        headers: {
          'Authorization': `Token ${localStorage.getItem('token')}`,
        },
      });
      const data = await response.json();
      setProducts(data.results || []);
    } catch (error) {
      console.error('Error loading products:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load products',
        severity: 'error',
      });
    }
  };

  const loadOutputTaxes = async () => {
    try {
      const allTaxes = await salesTaxService.getAllSalesTaxes();
      const outputTaxes = allTaxes.filter(tax => tax.tax_type === 'output');
      setOutputTaxes(outputTaxes);
    } catch (error) {
      console.error('Error loading output taxes:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load sales tax rates',
        severity: 'error',
      });
    }
  };

  // Transform products to account options for JournalLineTable
  const transformProductsToAccountOptions = () => {
    return products.map(product => ({
      id: product.id,
      account_name: product.name,
      account_number: product.id.toString(),
      balance: product.unit_price,
    }));
  };

  // Transform output taxes to sales tax options for JournalLineTable
  const transformOutputTaxesToSalesTaxOptions = () => {
    return outputTaxes.map(tax => ({
      id: tax.id,
      description: tax.description,
      rate: tax.rate,
      tax_type: tax.tax_type,
    }));
  };

  // Handle customer selection
  const handleCustomerChange = (customer: InvoiceCustomer | null) => {
    setSelectedCustomer(customer);
    setFormData(prev => ({
      ...prev,
      customer_id: customer?.id || null,
    }));
  };

  // Handle form field changes
  const handleFieldChange = (field: keyof InvoiceFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Calculate totals
  const calculateTotals = useCallback((lineItems: InvoiceLineItem[]) => {
    const subtotal = lineItems.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const totalTax = lineItems.reduce((sum, item) => sum + (item.sales_tax_amount || 0), 0);
    const totalAmount = subtotal + totalTax;

    setFormData(prev => ({
      ...prev,
      subtotal,
      total_tax: totalTax,
      total_amount: totalAmount,
      balance_due: totalAmount,
    }));
  }, []);

  // Handle line item changes
  const handleLineChange = (lineId: string, field: string, value: any) => {
    console.log('Line change:', { lineId, field, value });

    setFormData(prev => {
      const updatedLineItems = prev.line_items.map(item => {
        if (item.id === lineId) {
          const updatedItem = { ...item, [field]: value };

          // Auto-calculate based on field changes
          if (field === 'account_selection') {
            // Product selected from JournalLineTable
            const selectedAccount = value;
            console.log('Selected account/product:', selectedAccount);

            if (selectedAccount) {
              // Find the corresponding product
              const product = products.find(p => p.id === selectedAccount.id);
              console.log('Found product:', product);

              if (product) {
                updatedItem.account_id = product.id;
                updatedItem.product_id = product.id;
                updatedItem.product_name = product.name;
                updatedItem.description = product.description || product.name;
                updatedItem.unit_price = product.unit_price;
                updatedItem.line_total = (updatedItem.quantity || 1) * product.unit_price;

                // Add account_name for display in table
                updatedItem.account_name = product.name;
              }
            } else {
              // Clear selection
              updatedItem.account_id = null;
              updatedItem.product_id = null;
              updatedItem.product_name = '';
              updatedItem.description = '';
              updatedItem.unit_price = 0;
              updatedItem.line_total = 0;
            }
          } else if (field === 'account_id') {
            // Direct product ID selection
            const product = products.find(p => p.id === value);
            console.log('Selected product by ID:', product);
            if (product) {
              updatedItem.product_id = product.id;
              updatedItem.product_name = product.name;
              updatedItem.description = product.description || product.name;
              updatedItem.unit_price = product.unit_price;
              updatedItem.line_total = (updatedItem.quantity || 1) * product.unit_price;

              // Keep the account_id for the table display
              updatedItem.account_id = value;
            }
          } else if (field === 'quantity') {
            // Recalculate line total
            const quantity = value || 0;
            const unitPrice = updatedItem.unit_price || 0;
            updatedItem.quantity = quantity;
            updatedItem.line_total = quantity * unitPrice;

            // Recalculate tax if applicable
            if (updatedItem.sales_tax_rate > 0) {
              updatedItem.sales_tax_amount = (updatedItem.line_total * updatedItem.sales_tax_rate) / 100;
            }
          } else if (field === 'unit_price') {
            // Recalculate line total
            const quantity = updatedItem.quantity || 1;
            const unitPrice = value || 0;
            updatedItem.unit_price = unitPrice;
            updatedItem.line_total = quantity * unitPrice;

            // Recalculate tax if applicable
            if (updatedItem.sales_tax_rate > 0) {
              updatedItem.sales_tax_amount = (updatedItem.line_total * updatedItem.sales_tax_rate) / 100;
            }
          } else if (field === 'sales_tax_id') {
            // Sales tax selected
            const tax = outputTaxes.find(t => t.id === value);
            if (tax) {
              updatedItem.sales_tax_id = value;
              updatedItem.sales_tax_rate = tax.rate;
              updatedItem.sales_tax_amount = (updatedItem.line_total * tax.rate) / 100;
            } else {
              updatedItem.sales_tax_id = null;
              updatedItem.sales_tax_rate = 0;
              updatedItem.sales_tax_amount = 0;
            }
          } else if (field === 'description') {
            updatedItem.description = value;
          }

          return updatedItem;
        }
        return item;
      });

      calculateTotals(updatedLineItems);
      return { ...prev, line_items: updatedLineItems };
    });
  };

  // Add new line item
  const handleAddLine = () => {
    const newLine: InvoiceLineItem = {
      id: `line_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      account_id: null,
      account_name: '',
      description: '',
      memo: '',
      amount: 0,
      product_id: null,
      product_name: '',
      quantity: 1,
      unit_price: 0,
      line_total: 0,
      sales_tax_id: null,
      sales_tax_rate: 0,
      sales_tax_amount: 0,
    };

    console.log('Adding new line:', newLine);

    setFormData(prev => ({
      ...prev,
      line_items: [...prev.line_items, newLine],
    }));
  };

  // Remove line item
  const handleRemoveLine = (lineId: string) => {
    setFormData(prev => {
      const updatedLineItems = prev.line_items.filter(item => item.id !== lineId);
      calculateTotals(updatedLineItems);
      return { ...prev, line_items: updatedLineItems };
    });
  };

  // Validate form data
  const validateForm = (): string[] => {
    const errors: string[] = [];

    if (!formData.customer_id) {
      errors.push('Please select a customer');
    }

    if (!formData.invoice_date) {
      errors.push('Invoice date is required');
    }

    if (!formData.due_date) {
      errors.push('Due date is required');
    }

    if (formData.line_items.length === 0) {
      errors.push('At least one line item is required');
    }

    // Validate line items
    formData.line_items.forEach((item, index) => {
      if (!item.product_id) {
        errors.push(`Line ${index + 1}: Please select a product/service`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Line ${index + 1}: Quantity must be greater than 0`);
      }
      if (!item.unit_price || item.unit_price < 0) {
        errors.push(`Line ${index + 1}: Unit price must be 0 or greater`);
      }
    });

    return errors;
  };

  // Handle save
  const handleSave = async () => {
    const errors = validateForm();
    if (errors.length > 0) {
      setSnackbar({
        open: true,
        message: errors.join(', '),
        severity: 'error',
      });
      return;
    }

    setLoading(true);
    try {
      // Prepare data for API
      const invoiceData = {
        customer: formData.customer_id,
        warehouse: 1, // Default warehouse - should be configurable
        invoice_date: formData.invoice_date,
        due_date: formData.due_date,
        terms: formData.terms,
        notes: formData.notes,
        po_number: formData.po_number,
        line_items: formData.line_items.map(item => ({
          product_id: item.product_id,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
        })),
        auto_apply_pricing: true,
        validate_inventory: true,
        issue_inventory: false,
      };

      let response;
      if (invoice?.id) {
        // Update existing invoice
        response = await invoiceService.updateInvoice(invoice.id, invoiceData);
      } else {
        // Create new invoice
        response = await invoiceService.createInvoice(invoiceData);
      }

      setSnackbar({
        open: true,
        message: `Invoice ${invoice?.id ? 'updated' : 'created'} successfully!`,
        severity: 'success',
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error('Error saving invoice:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Failed to save invoice',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: fullPage ? 3 : 0,
      }}>
        {/* Header */}
        <Box sx={{
          mb: 3,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexShrink: 0,
        }}>
          <Typography variant="h4" fontWeight="bold">
            Invoice no.{formData.invoice_number || 'New'}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              size="small"
            >
              Print or Preview
            </Button>
            <Button
              variant="outlined"
              startIcon={<SendIcon />}
              size="small"
            >
              Make recurring
            </Button>
          </Box>
        </Box>

        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'auto',
        }}>
          <Box sx={{ p: 3, flex: 1 }}>
          {/* Customer and Invoice Details */}
          <Grid container spacing={3}>
            {/* Left Column - Customer */}
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Customer
              </Typography>
              <Autocomplete
                options={customers}
                getOptionLabel={(option) => option.display_name || `Customer ${option.id}`}
                value={selectedCustomer}
                onChange={(_, newValue) => handleCustomerChange(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    placeholder={customers.length > 0 ? "Select a customer" : "Loading customers..."}
                    fullWidth
                    size="medium"
                  />
                )}
                sx={{ mb: 2 }}
                loading={customers.length === 0}
                noOptionsText={customers.length === 0 ? "Loading customers..." : "No customers found"}
              />
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Billing address
              </Typography>
              <TextField
                multiline
                rows={3}
                placeholder="Enter billing address"
                fullWidth
                size="small"
                value={selectedCustomer?.billing_address || ''}
                InputProps={{ readOnly: true }}
              />
            </Grid>

            {/* Right Column - Invoice Details */}
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6">
                  Balance due
                </Typography>
                <Typography variant="h4" fontWeight="bold">
                  ${formData.balance_due.toFixed(2)}
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Terms
                  </Typography>
                  <FormControl fullWidth size="small">
                    <Select
                      value={formData.terms}
                      onChange={(e) => handleFieldChange('terms', e.target.value)}
                    >
                      <MenuItem value="Due on receipt">Due on receipt</MenuItem>
                      <MenuItem value="Net 15">Net 15</MenuItem>
                      <MenuItem value="Net 30">Net 30</MenuItem>
                      <MenuItem value="Net 60">Net 60</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Invoice date
                  </Typography>
                  <DatePicker
                    value={dayjs(formData.invoice_date)}
                    onChange={(newValue) => 
                      handleFieldChange('invoice_date', newValue?.format('YYYY-MM-DD') || '')
                    }
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Due date
                  </Typography>
                  <DatePicker
                    value={dayjs(formData.due_date)}
                    onChange={(newValue) => 
                      handleFieldChange('due_date', newValue?.format('YYYY-MM-DD') || '')
                    }
                    slotProps={{
                      textField: {
                        size: 'small',
                        fullWidth: true,
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Invoice no.
                  </Typography>
                  <TextField
                    size="small"
                    fullWidth
                    value={formData.invoice_number || ''}
                    onChange={(e) => handleFieldChange('invoice_number', e.target.value)}
                    placeholder="Auto-generated"
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Tags and PO Number */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Tags
              </Typography>
              <TextField
                size="small"
                fullWidth
                placeholder="Start typing to add a tag..."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Message on invoice
              </Typography>
              <TextField
                size="small"
                fullWidth
                placeholder="Enter message"
              />
            </Grid>
          </Grid>

          {/* Line Items Table */}
          <Box sx={{ mb: 3 }}>
            <JournalLineTable
              tableMode="custom"
              showAccountColumn
              showDescriptionColumn
              showMemoColumn={false}
              showAmountColumn={false}
              showDebitCreditColumns={false}
              showSalesTaxColumn
              showActionsColumn
              lines={formData.line_items}
              accounts={transformProductsToAccountOptions()}
              salesTaxes={transformOutputTaxesToSalesTaxOptions()}
              onLineChange={handleLineChange}
              onAddLine={handleAddLine}
              onRemoveLine={handleRemoveLine}
              currencySymbol="$"
              accountColumnLabel="PRODUCT/SERVICE"
              accountPlaceholder="Select Product/Service"
              descriptionPlaceholder="Enter description"
              salesTaxColumnLabel="SALES TAX"
              salesTaxPlaceholder="Select Sales Tax"
              additionalColumns={[
                {
                  key: 'quantity',
                  label: 'QTY',
                  width: 80,
                  render: (line, onChange) => (
                    <QuantityInput
                      value={line.quantity || 1}
                      onChange={(value) => onChange(value)}
                      min={0}
                      step={1}
                      size="small"
                      fullWidth
                    />
                  ),
                },
                {
                  key: 'unit_price',
                  label: 'RATE (CAD)',
                  width: 120,
                  render: (line, onChange) => (
                    <FormattedCurrencyInput
                      name={`unit_price_${line.id}`}
                      value={line.unit_price || 0}
                      onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
                      size="small"
                      fullWidth
                    />
                  ),
                },
                {
                  key: 'line_total',
                  label: 'AMOUNT (CAD)',
                  width: 120,
                  render: (line) => (
                    <Typography variant="body2" sx={{ textAlign: 'right', py: 1 }}>
                      ${(line.line_total || 0).toFixed(2)}
                    </Typography>
                  ),
                },
              ]}
              minLines={1}
              tableHeight="300px"
              readOnly={false}
              loading={loading}
            />
          </Box>

          {/* Totals Section */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
            <Box sx={{ width: 300 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Subtotal</Typography>
                <Typography variant="body2">${formData.subtotal.toFixed(2)}</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">
                  Discount percent
                  <Autocomplete
                    size="small"
                    options={[]}
                    sx={{ width: 100, display: 'inline-block', ml: 1 }}
                    renderInput={(params) => <TextField {...params} />}
                  />
                </Typography>
                <Typography variant="body2">0.00</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Total</Typography>
                <Typography variant="body2">${formData.total_amount.toFixed(2)}</Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6" fontWeight="bold">Balance due</Typography>
                <Typography variant="h6" fontWeight="bold">
                  ${formData.balance_due.toFixed(2)}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Notes Section */}
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Message on invoice
              </Typography>
              <TextField
                multiline
                rows={3}
                fullWidth
                placeholder="Thank you for your business!"
                value={formData.notes}
                onChange={(e) => handleFieldChange('notes', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Message on statement
              </Typography>
              <TextField
                multiline
                rows={3}
                fullWidth
                placeholder="Thank you for your business!"
              />
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              variant="outlined"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<PrintIcon />}
              >
                Print or Preview
              </Button>
              <Button
                variant="outlined"
                startIcon={<SendIcon />}
              >
                Make recurring
              </Button>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSave}
                disabled={loading}
              >
                {loading ? 'Saving...' : 'Save'}
              </Button>
            </Box>
          </Box>
          </Box>
        </Box>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default EnhancedInvoiceForm;
