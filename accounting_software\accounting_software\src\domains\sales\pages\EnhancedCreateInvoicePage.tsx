import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Breadcrumbs,
  Link,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import EnhancedInvoiceForm from '../components/EnhancedInvoiceForm';

const EnhancedCreateInvoicePage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);

  const handleSuccess = () => {
    navigate('/sales/invoices');
  };

  const handleCancel = () => {
    navigate('/sales/invoices');
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      display: 'flex',
      flexDirection: 'column',
    }}>
      {/* Header */}
      <Box sx={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e0e0e0',
        px: 3,
        py: 2,
        flexShrink: 0,
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Tooltip title="Back to Invoices">
            <IconButton
              onClick={() => navigate('/sales/invoices')}
              sx={{ mr: 2 }}
            >
              <ArrowBackIcon />
            </IconButton>
          </Tooltip>

          <Breadcrumbs aria-label="breadcrumb">
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              color="inherit"
              onClick={() => navigate('/dashboard')}
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Link
              underline="hover"
              sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
              color="inherit"
              onClick={() => navigate('/sales/invoices')}
            >
              <ReceiptIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Invoices
            </Link>
            <Typography color="text.primary">
              {isEditing ? 'Edit Invoice' : 'Create Invoice'}
            </Typography>
          </Breadcrumbs>
        </Box>
      </Box>

      {/* Invoice Form - Full Screen */}
      <Box sx={{
        flex: 1,
        overflow: 'auto',
        backgroundColor: 'white',
      }}>
        <EnhancedInvoiceForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
          fullPage={true}
        />
      </Box>
    </Box>
  );
};

export default EnhancedCreateInvoicePage;
