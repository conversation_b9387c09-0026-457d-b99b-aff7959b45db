#!/usr/bin/env python3
"""
Test loading all customers with pagination
"""
import requests

def test_all_customers():
    """Test loading all customers"""
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("🔍 Testing All Customer Loading")
    print("=" * 40)
    
    all_customers = []
    next_url = 'http://localhost:8000/api/contacts/customers/?page_size=100'
    page_num = 1
    
    while next_url:
        print(f"\n📄 Loading page {page_num}...")
        print(f"   URL: {next_url}")
        
        try:
            response = requests.get(next_url, headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                page_customers = data.get('results', [])
                all_customers.extend(page_customers)
                
                print(f"   ✅ Loaded {len(page_customers)} customers from page {page_num}")
                print(f"   📊 Total so far: {len(all_customers)}")
                
                # Show first few customers from this page
                for i, customer in enumerate(page_customers[:3]):
                    print(f"      {len(all_customers) - len(page_customers) + i + 1}. {customer.get('display_name', 'N/A')} (ID: {customer.get('id')})")
                
                if len(page_customers) > 3:
                    print(f"      ... and {len(page_customers) - 3} more")
                
                # Check for next page
                next_url = data.get('next')
                if next_url:
                    print(f"   🔗 Next page: {next_url}")
                else:
                    print(f"   🏁 No more pages")
                
                page_num += 1
            else:
                print(f"   ❌ Error: {response.text}")
                break
                
        except Exception as e:
            print(f"   💥 Exception: {e}")
            break
    
    print(f"\n🎯 Final Results:")
    print(f"   Total customers loaded: {len(all_customers)}")
    print(f"   Pages processed: {page_num - 1}")
    
    if all_customers:
        print(f"\n📋 All Customer Names:")
        for i, customer in enumerate(all_customers, 1):
            print(f"   {i:2d}. {customer.get('display_name', 'N/A')} (ID: {customer.get('id')})")

if __name__ == "__main__":
    test_all_customers()
