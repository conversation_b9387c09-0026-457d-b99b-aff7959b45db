#!/usr/bin/env python3
"""
Test customer dropdown data for invoice form
"""
import requests

def test_customer_dropdown():
    """Test customer data for dropdown"""
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("🔍 Testing Customer Dropdown Data")
    print("=" * 40)
    
    # Test customer endpoint
    print("\n1️⃣ Testing GET /api/contacts/customers/")
    try:
        response = requests.get("http://localhost:8000/api/contacts/customers/", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            customers = data.get('results', [])
            print(f"   ✅ Success! Found {len(customers)} customers")
            
            # Show first few customers
            for i, customer in enumerate(customers[:3]):
                print(f"   📋 Customer {i+1}:")
                print(f"      ID: {customer.get('id')}")
                print(f"      Name: {customer.get('display_name', 'N/A')}")
                print(f"      Email: {customer.get('email', 'N/A')}")
                print(f"      Phone: {customer.get('phone', 'N/A')}")
                print()
                
        else:
            print(f"   ❌ Error: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test products endpoint
    print("\n2️⃣ Testing GET /api/sales/products/")
    try:
        response = requests.get("http://localhost:8000/api/sales/products/", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            products = data.get('results', [])
            print(f"   ✅ Success! Found {len(products)} products")
            
            # Show first few products
            for i, product in enumerate(products[:3]):
                print(f"   📦 Product {i+1}:")
                print(f"      ID: {product.get('id')}")
                print(f"      Name: {product.get('name', 'N/A')}")
                print(f"      Price: ${product.get('unit_price', 0)}")
                print(f"      Type: {product.get('type', 'N/A')}")
                print()
                
        else:
            print(f"   ❌ Error: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    test_customer_dropdown()
