import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Autocomplete,
  TextField,
  Paper,
  Alert,
} from '@mui/material';

interface Customer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
}

const TestCustomerDropdown: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCustomers = async () => {
    console.log('🔍 Testing customer dropdown...');
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token');
      console.log('🔑 Token exists:', !!token);
      
      const response = await fetch('http://localhost:8000/api/contacts/customers/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      console.log('📡 Response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Response data:', data);
        
        const customerList = data.results || [];
        console.log('👥 Customers:', customerList);
        
        setCustomers(customerList);
      } else {
        const errorText = await response.text();
        console.error('❌ Error response:', errorText);
        setError(`Failed to load customers: ${response.status}`);
      }
    } catch (err: any) {
      console.error('💥 Exception:', err);
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCustomers();
  }, []);

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Customer Dropdown Test
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Customer Selection
        </Typography>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Found {customers.length} customers
        </Typography>
        
        <Autocomplete
          options={customers}
          getOptionLabel={(option) => option.display_name || `Customer ${option.id}`}
          value={selectedCustomer}
          onChange={(_, newValue) => {
            console.log('🎯 Selected customer:', newValue);
            setSelectedCustomer(newValue);
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Select Customer"
              placeholder={loading ? "Loading..." : "Choose a customer"}
              fullWidth
            />
          )}
          loading={loading}
          noOptionsText={loading ? "Loading customers..." : "No customers found"}
          sx={{ mb: 2 }}
        />
        
        {selectedCustomer && (
          <Box sx={{ mt: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="subtitle2">Selected Customer:</Typography>
            <Typography variant="body2">ID: {selectedCustomer.id}</Typography>
            <Typography variant="body2">Name: {selectedCustomer.display_name}</Typography>
            <Typography variant="body2">Email: {selectedCustomer.email || 'N/A'}</Typography>
            <Typography variant="body2">Phone: {selectedCustomer.phone || 'N/A'}</Typography>
          </Box>
        )}
        
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2">Debug Info:</Typography>
          <Typography variant="body2">Loading: {loading.toString()}</Typography>
          <Typography variant="body2">Customer count: {customers.length}</Typography>
          <Typography variant="body2">Token exists: {!!localStorage.getItem('token')}</Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default TestCustomerDropdown;
