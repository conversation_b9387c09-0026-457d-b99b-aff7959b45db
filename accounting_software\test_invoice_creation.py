#!/usr/bin/env python3
"""
Test invoice creation with the new enhanced serializer
"""
import requests
import json

def test_invoice_creation():
    """Test creating an invoice with the enhanced serializer"""
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("🧾 Testing Enhanced Invoice Creation")
    print("=" * 50)
    
    # Get test data first
    print("\n1️⃣ Getting test data...")
    
    # Get customer
    customers_response = requests.get("http://localhost:8000/api/contacts/customers/", headers=headers)
    if customers_response.status_code != 200:
        print("❌ Failed to get customers")
        return
    
    customers = customers_response.json()
    if not customers.get('results'):
        print("❌ No customers found")
        return
    
    customer_id = customers['results'][0]['id']
    print(f"   ✅ Using customer ID: {customer_id}")
    
    # Get warehouse
    warehouses_response = requests.get("http://localhost:8000/api/inventory/warehouses/", headers=headers)
    if warehouses_response.status_code != 200:
        print("❌ Failed to get warehouses")
        return
    
    warehouses = warehouses_response.json()
    if not warehouses.get('results'):
        print("❌ No warehouses found")
        return
    
    warehouse_id = warehouses['results'][0]['warehouse_id']
    print(f"   ✅ Using warehouse ID: {warehouse_id}")
    
    # Get products
    products_response = requests.get("http://localhost:8000/api/sales/products/", headers=headers)
    if products_response.status_code != 200:
        print("❌ Failed to get products")
        return
    
    products = products_response.json()
    if not products.get('results'):
        print("❌ No products found")
        return
    
    product1 = products['results'][0]
    product2 = products['results'][1] if len(products['results']) > 1 else product1
    print(f"   ✅ Using products: {product1['name']}, {product2['name']}")
    
    # Test 2: Create invoice with enhanced serializer
    print("\n2️⃣ Creating invoice with enhanced serializer...")
    
    invoice_data = {
        "customer": customer_id,
        "warehouse": warehouse_id,
        "invoice_date": "2024-01-15",
        "due_date": "2024-02-14",
        "terms": "Net 30",
        "notes": "Test invoice with enhanced serializer",
        "line_items": [
            {
                "product_id": product1['id'],
                "quantity": 2,
                "description": f"Test item - {product1['name']}"
            },
            {
                "product_id": product2['id'],
                "quantity": 1,
                "description": f"Test item - {product2['name']}"
            }
        ],
        "auto_apply_pricing": True,
        "validate_inventory": True,
        "issue_inventory": False
    }
    
    print(f"   📤 Sending invoice data:")
    print(f"      Customer: {customer_id}")
    print(f"      Warehouse: {warehouse_id}")
    print(f"      Line items: {len(invoice_data['line_items'])}")
    
    response = requests.post("http://localhost:8000/api/sales/invoices/", json=invoice_data, headers=headers)
    
    print(f"   📥 Response status: {response.status_code}")
    
    if response.status_code == 201:
        invoice = response.json()
        print(f"   ✅ Invoice created successfully!")
        print(f"      Invoice ID: {invoice.get('id')}")
        print(f"      Invoice Number: {invoice.get('invoice_number', 'N/A')}")
        print(f"      Total Amount: ${invoice.get('total_amount', 0)}")
        print(f"      Subtotal: ${invoice.get('subtotal', 0)}")
        
        # Test 3: Get the created invoice
        print(f"\n3️⃣ Retrieving created invoice...")
        get_response = requests.get(f"http://localhost:8000/api/sales/invoices/{invoice['id']}/", headers=headers)
        
        if get_response.status_code == 200:
            retrieved_invoice = get_response.json()
            print(f"   ✅ Invoice retrieved successfully!")
            print(f"      Line items: {len(retrieved_invoice.get('line_items', []))}")
            print(f"      Customer name: {retrieved_invoice.get('customer_name', 'N/A')}")
        else:
            print(f"   ❌ Failed to retrieve invoice: {get_response.status_code}")
            print(f"      Error: {get_response.text}")
        
    else:
        print(f"   ❌ Failed to create invoice")
        print(f"      Error: {response.text}")
        
        # Try to parse the error
        try:
            error_data = response.json()
            print(f"      Parsed error: {json.dumps(error_data, indent=2)}")
        except:
            pass

if __name__ == "__main__":
    test_invoice_creation()
