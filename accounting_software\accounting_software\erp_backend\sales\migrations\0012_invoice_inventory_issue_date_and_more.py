# Generated by Django 4.2.21 on 2025-06-28 09:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Pricing', '0002_alter_pricelistitem_product'),
        ('inventory', '0005_alter_goodsreturnnote_vendor'),
        ('sales', '0011_update_customer_references'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='inventory_issue_date',
            field=models.DateTimeField(blank=True, help_text='When inventory was issued', null=True),
        ),
        migrations.AddField(
            model_name='invoice',
            name='inventory_issued',
            field=models.BooleanField(default=False, help_text='Whether inventory has been issued for this invoice'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='warehouse',
            field=models.ForeignKey(blank=True, help_text='Warehouse from which inventory will be issued', null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.warehouse'),
        ),
        migrations.AddField(
            model_name='invoicelineitem',
            name='inventory_cost',
            field=models.DecimalField(decimal_places=4, default=0.0, help_text='Actual cost of inventory issued', max_digits=15),
        ),
        migrations.AddField(
            model_name='invoicelineitem',
            name='inventory_issued',
            field=models.BooleanField(default=False, help_text='Whether inventory has been issued for this line item'),
        ),
        migrations.AddField(
            model_name='invoicelineitem',
            name='original_unit_price',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Original price before any manual adjustments', max_digits=15),
        ),
        migrations.AddField(
            model_name='invoicelineitem',
            name='price_list_used',
            field=models.ForeignKey(blank=True, help_text='Price list used for this line item', null=True, on_delete=django.db.models.deletion.SET_NULL, to='Pricing.pricelist'),
        ),
        migrations.AddField(
            model_name='invoicelineitem',
            name='stock_transaction_id',
            field=models.PositiveIntegerField(blank=True, help_text='Reference to stock transaction record', null=True),
        ),
    ]
