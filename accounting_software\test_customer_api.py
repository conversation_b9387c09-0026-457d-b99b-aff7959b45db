#!/usr/bin/env python3
"""
Test script to debug customer creation API
"""
import requests
import json

# Test data
test_customer_data = {
    "displayName": "Test Customer API",
    "firstName": "Test",
    "lastName": "Customer",
    "companyName": "Test Company API",
    "email": "<EMAIL>",
    "phone": "************",
    "mobile": "************",
    "billingAddress": {
        "street": "123 Test St",
        "city": "Test City",
        "state": "Test State",
        "postalCode": "12345",
        "country": "US"
    },
    "shippingAddress": {
        "sameAsBilling": True,
        "street": "",
        "city": "",
        "state": "",
        "postalCode": "",
        "country": "US"
    },
    "paymentTerms": "net_30",
    "creditLimit": 5000,
    "customerCategory": "business",
    "discountPercentage": 5,
    "taxExempt": False,
    "notes": "Test customer via API",
    "is_active": True
}

def test_customer_creation():
    """Test customer creation API"""
    url = "http://localhost:8000/api/contacts/customers/"
    
    # Use the token from debug file
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"

    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("Testing customer creation API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_customer_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_customer_data, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200 or response.status_code == 201:
            print("✅ SUCCESS!")
            print(f"Response Data: {json.dumps(response.json(), indent=2)}")
        else:
            print("❌ FAILED!")
            print(f"Response Text: {response.text}")
            
            # Try to parse as JSON for better error details
            try:
                error_data = response.json()
                print(f"Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

def test_customer_list():
    """Test customer list API"""
    url = "http://localhost:8000/api/contacts/customers/"
    
    # Use the token from debug file
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"

    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("\nTesting customer list API...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            data = response.json()
            print(f"Found {data.get('count', 0)} customers")
            if data.get('results'):
                print(f"First customer: {data['results'][0].get('display_name', 'N/A')}")
        else:
            print("❌ FAILED!")
            print(f"Response Text: {response.text}")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("Customer API Test Script")
    print("=" * 50)
    
    # Test list first (should work)
    test_customer_list()
    
    # Test creation (this is what's failing)
    test_customer_creation()
