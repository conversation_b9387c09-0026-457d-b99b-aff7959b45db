"""
Django management command to set up database functions for inventory management.
This creates database-level functions for fast inventory operations.
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.conf import settings


class Command(BaseCommand):
    help = 'Set up database functions for inventory management'

    def handle(self, *args, **options):
        """Create database functions for inventory operations"""
        
        self.stdout.write('Setting up inventory database functions...')
        
        # Check if we're using SQLite or PostgreSQL
        db_engine = settings.DATABASES['default']['ENGINE']
        
        if 'sqlite' in db_engine:
            self.setup_sqlite_functions()
        elif 'postgresql' in db_engine:
            self.setup_postgresql_functions()
        else:
            self.stdout.write(
                self.style.WARNING(
                    f'Database engine {db_engine} not supported for custom functions'
                )
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up inventory database functions')
        )

    def setup_sqlite_functions(self):
        """Set up SQLite functions for inventory management"""
        with connection.cursor() as cursor:
            
            # Function to check inventory availability
            # Drop view if exists (SQLite doesn't support CREATE OR REPLACE VIEW)
            cursor.execute("DROP VIEW IF EXISTS inventory_availability")
            cursor.execute("""
                CREATE VIEW inventory_availability AS
                SELECT 
                    i.inventory_id,
                    i.product_id,
                    i.warehouse_id,
                    i.quantity_on_hand,
                    i.quantity_reserved,
                    (i.quantity_on_hand - i.quantity_reserved) AS available_quantity,
                    p.name AS product_name,
                    w.name AS warehouse_name,
                    w.warehouse_type,
                    CASE 
                        WHEN (i.quantity_on_hand - i.quantity_reserved) <= 0 THEN 'OUT_OF_STOCK'
                        WHEN (i.quantity_on_hand - i.quantity_reserved) <= i.reorder_point THEN 'LOW_STOCK'
                        ELSE 'IN_STOCK'
                    END AS stock_status
                FROM inventory i
                JOIN sales_products p ON i.product_id = p.id
                JOIN inventory_warehouses w ON i.warehouse_id = w.warehouse_id
                WHERE w.is_active = 1 AND p.is_active = 1
            """)
            
            # Function to get available quantity for a specific product and warehouse
            cursor.execute("DROP VIEW IF EXISTS product_warehouse_availability")
            cursor.execute("""
                CREATE VIEW product_warehouse_availability AS
                SELECT 
                    product_id,
                    warehouse_id,
                    SUM(quantity_on_hand - quantity_reserved) AS total_available,
                    COUNT(*) AS inventory_records,
                    MIN(quantity_on_hand - quantity_reserved) AS min_available,
                    MAX(quantity_on_hand - quantity_reserved) AS max_available
                FROM inventory
                GROUP BY product_id, warehouse_id
            """)

    def setup_postgresql_functions(self):
        """Set up PostgreSQL functions for inventory management"""
        with connection.cursor() as cursor:
            
            # Function to check and reserve inventory
            cursor.execute("""
                CREATE OR REPLACE FUNCTION check_and_reserve_inventory(
                    p_product_id INTEGER,
                    p_warehouse_id INTEGER,
                    p_quantity DECIMAL(15,4),
                    p_reference_type VARCHAR(20),
                    p_reference_id INTEGER
                ) RETURNS JSON AS $$
                DECLARE
                    v_available DECIMAL(15,4);
                    v_result JSON;
                BEGIN
                    -- Get current available quantity
                    SELECT (quantity_on_hand - quantity_reserved) 
                    INTO v_available
                    FROM inventory 
                    WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id;
                    
                    -- Check if we have enough stock
                    IF v_available IS NULL THEN
                        v_result := json_build_object(
                            'success', false,
                            'error', 'Product not found in warehouse',
                            'available', 0,
                            'requested', p_quantity
                        );
                    ELSIF v_available < p_quantity THEN
                        v_result := json_build_object(
                            'success', false,
                            'error', 'Insufficient stock',
                            'available', v_available,
                            'requested', p_quantity
                        );
                    ELSE
                        -- Reserve the inventory
                        UPDATE inventory 
                        SET quantity_reserved = quantity_reserved + p_quantity,
                            last_updated = NOW()
                        WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id;
                        
                        -- Create stock transaction record
                        INSERT INTO stock_transactions (
                            product_id, warehouse_id, transaction_type, reference_type,
                            reference_id, quantity, unit_cost, txn_date, created_by_id
                        ) VALUES (
                            p_product_id, p_warehouse_id, 'RESERVATION', p_reference_type,
                            p_reference_id, p_quantity, 0, NOW(), 1
                        );
                        
                        v_result := json_build_object(
                            'success', true,
                            'reserved', p_quantity,
                            'remaining_available', v_available - p_quantity
                        );
                    END IF;
                    
                    RETURN v_result;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # Function to issue inventory (reduce stock)
            cursor.execute("""
                CREATE OR REPLACE FUNCTION issue_inventory(
                    p_product_id INTEGER,
                    p_warehouse_id INTEGER,
                    p_quantity DECIMAL(15,4),
                    p_reference_type VARCHAR(20),
                    p_reference_id INTEGER,
                    p_unit_cost DECIMAL(15,4) DEFAULT 0
                ) RETURNS JSON AS $$
                DECLARE
                    v_reserved DECIMAL(15,4);
                    v_on_hand DECIMAL(15,4);
                    v_result JSON;
                BEGIN
                    -- Get current quantities
                    SELECT quantity_on_hand, quantity_reserved 
                    INTO v_on_hand, v_reserved
                    FROM inventory 
                    WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id;
                    
                    -- Check if we have enough stock
                    IF v_on_hand IS NULL THEN
                        v_result := json_build_object(
                            'success', false,
                            'error', 'Product not found in warehouse'
                        );
                    ELSIF v_on_hand < p_quantity THEN
                        v_result := json_build_object(
                            'success', false,
                            'error', 'Insufficient stock for issue',
                            'on_hand', v_on_hand,
                            'requested', p_quantity
                        );
                    ELSE
                        -- Issue the inventory
                        UPDATE inventory 
                        SET quantity_on_hand = quantity_on_hand - p_quantity,
                            quantity_reserved = GREATEST(0, quantity_reserved - p_quantity),
                            last_transaction_date = NOW(),
                            last_updated = NOW()
                        WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id;
                        
                        -- Create stock transaction record
                        INSERT INTO stock_transactions (
                            product_id, warehouse_id, transaction_type, reference_type,
                            reference_id, quantity, unit_cost, total_cost, txn_date, created_by_id
                        ) VALUES (
                            p_product_id, p_warehouse_id, 'ISSUE', p_reference_type,
                            p_reference_id, -p_quantity, p_unit_cost, -(p_quantity * p_unit_cost), NOW(), 1
                        );
                        
                        v_result := json_build_object(
                            'success', true,
                            'issued', p_quantity,
                            'remaining_on_hand', v_on_hand - p_quantity
                        );
                    END IF;
                    
                    RETURN v_result;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # Function to get inventory availability with stock status
            cursor.execute("""
                CREATE OR REPLACE VIEW inventory_availability AS
                SELECT 
                    i.inventory_id,
                    i.product_id,
                    i.warehouse_id,
                    i.quantity_on_hand,
                    i.quantity_reserved,
                    (i.quantity_on_hand - i.quantity_reserved) AS available_quantity,
                    p.name AS product_name,
                    p.sku AS product_sku,
                    w.name AS warehouse_name,
                    w.warehouse_type,
                    CASE 
                        WHEN (i.quantity_on_hand - i.quantity_reserved) <= 0 THEN 'OUT_OF_STOCK'
                        WHEN (i.quantity_on_hand - i.quantity_reserved) <= i.reorder_point THEN 'LOW_STOCK'
                        ELSE 'IN_STOCK'
                    END AS stock_status,
                    i.reorder_point,
                    i.average_cost,
                    i.last_cost
                FROM inventory i
                JOIN sales_products p ON i.product_id = p.id
                JOIN inventory_warehouses w ON i.warehouse_id = w.warehouse_id
                WHERE w.is_active = true AND p.is_active = true
            """)
