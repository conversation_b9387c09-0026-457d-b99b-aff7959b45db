from django.utils import timezone
from decimal import Decimal
from .models import PriceList, PriceListItem, DiscountRule, CustomerPriceList

class PricingService:
    @staticmethod
    def get_customer_price_list(customer):
        """
        Get the active price list for a customer
        Falls back to default price list if no specific list is assigned
        """
        today = timezone.now().date()
        
        # First try to get specific price list for customer
        customer_price = CustomerPriceList.objects.filter(
            customer=customer,
            effective_date__lte=today,
            expiry_date__gte=today
        ).first()
        
        if customer_price:
            return customer_price.price_list
        
        # Fall back to default price list
        return PriceList.objects.filter(is_default=True, is_active=True).first()
    
    @staticmethod
    def get_product_price(product, customer=None, quantity=1, date=None):
        """
        Get the price for a product considering:
        - Customer-specific price lists
        - Quantity breaks
        - Active discounts
        """
        if date is None:
            date = timezone.now().date()
        
        quantity = Decimal(str(quantity))
        base_price = None
        final_price = None
        
        # Step 1: Get base price from price list
        if customer:
            price_list = PricingService.get_customer_price_list(customer)
        else:
            price_list = PriceList.objects.filter(is_default=True, is_active=True).first()
        
        if price_list:
            # Find the best price based on quantity break
            price_item = PriceListItem.objects.filter(
                price_list=price_list,
                product=product,
                min_quantity__lte=quantity,
                effective_date__lte=date,
                expiry_date__gte=date
            ).order_by('-min_quantity').first()
            
            if price_item:
                base_price = price_item.unit_price
                # Apply price list discount if any
                if price_item.discount_percent > 0:
                    base_price = base_price * (1 - price_item.discount_percent / 100)
        
        # If no price list item found, use product's standard price (if exists)
        if base_price is None:
            # In a real system, you might have a 'standard_price' field on Product
            # For now, we'll return None if no price is found
            return None
        
        final_price = base_price
        
        # Step 2: Apply any additional discount rules
        discount_rules = DiscountRule.objects.filter(
            is_active=True,
            start_date__lte=date,
            end_date__gte=date
        )
        
        # Apply customer group discounts
        if customer and customer.group:
            group_discounts = discount_rules.filter(
                customer_group=customer.group,
                min_quantity__lte=quantity
            ).order_by('-discount_percent')
            
            if group_discounts.exists():
                best_discount = group_discounts.first()
                final_price = final_price * (1 - best_discount.discount_percent / 100)
        
        # Apply category discounts
        if product.category:
            category_discounts = discount_rules.filter(
                product_category=product.category,
                min_quantity__lte=quantity
            ).order_by('-discount_percent')
            
            if category_discounts.exists():
                best_discount = category_discounts.first()
                final_price = final_price * (1 - best_discount.discount_percent / 100)
        
        return final_price.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_line_total(product, unit_price, quantity, tax_inclusive=False):
        """
        Calculate line total with tax
        """
        quantity = Decimal(str(quantity))
        unit_price = Decimal(str(unit_price))
        
        subtotal = unit_price * quantity
        
        if not product.tax_code:
            return {
                'subtotal': subtotal,
                'tax_amount': Decimal('0'),
                'total': subtotal
            }
        
        tax_rate = product.tax_code.rate / 100
        
        if tax_inclusive:
            # Price already includes tax
            tax_amount = subtotal - (subtotal / (1 + tax_rate))
            return {
                'subtotal': subtotal - tax_amount,
                'tax_amount': tax_amount,
                'total': subtotal
            }
        else:
            # Add tax to price
            tax_amount = subtotal * tax_rate
            return {
                'subtotal': subtotal,
                'tax_amount': tax_amount,
                'total': subtotal + tax_amount
            }

    @staticmethod
    def get_invoice_line_pricing(customer, product, quantity):
        """
        Get pricing for an invoice line item with all details needed for invoice creation

        Args:
            customer: Customer instance
            product: Product instance
            quantity: Quantity being purchased

        Returns:
            Dict with comprehensive pricing information
        """
        pricing_result = PricingService.get_product_price(product, customer, quantity)

        # Get the price list used
        price_list = PricingService.get_customer_price_list(customer)

        return {
            'success': True,
            'customer_id': customer.id,
            'product_id': product.id,
            'quantity': float(quantity),
            'unit_price': float(pricing_result['final_price']),
            'line_total': float(pricing_result['final_price'] * Decimal(str(quantity))),
            'base_price': float(pricing_result['base_price']) if pricing_result['base_price'] else 0,
            'discount_amount': float(pricing_result['discount_amount']),
            'discount_percentage': float(pricing_result['discount_percentage']),
            'price_list_id': price_list.id if price_list else None,
            'price_list_name': price_list.name if price_list else None,
            'discount_rule': pricing_result['discount_rule'],
            'currency': getattr(customer, 'currency', 'USD')
        }

    @staticmethod
    def calculate_invoice_totals(line_items_pricing):
        """
        Calculate invoice totals from line item pricing

        Args:
            line_items_pricing: List of pricing results from get_invoice_line_pricing

        Returns:
            Dict with invoice totals
        """
        subtotal = Decimal('0')
        total_discount = Decimal('0')
        total_base_amount = Decimal('0')

        for item in line_items_pricing:
            if item.get('success', False):
                subtotal += Decimal(str(item['line_total']))
                total_discount += Decimal(str(item['discount_amount'])) * Decimal(str(item['quantity']))
                total_base_amount += Decimal(str(item['base_price'])) * Decimal(str(item['quantity']))

        return {
            'subtotal': float(subtotal),
            'total_discount': float(total_discount),
            'total_base_amount': float(total_base_amount),
            'discount_percentage': float((total_discount / total_base_amount) * 100) if total_base_amount > 0 else 0,
            'line_count': len(line_items_pricing)
        }