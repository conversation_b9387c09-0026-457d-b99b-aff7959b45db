#!/usr/bin/env python3
"""
Simple API test to check if the server is working
"""
import requests

def test_simple_api():
    """Test basic API functionality"""
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("🔍 Testing Simple API Calls")
    print("=" * 40)
    
    # Test 1: Get invoices
    print("\n1️⃣ Testing GET /api/sales/invoices/")
    try:
        response = requests.get("http://localhost:8000/api/sales/invoices/", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data.get('results', []))} invoices")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Get customers
    print("\n2️⃣ Testing GET /api/contacts/customers/")
    try:
        response = requests.get("http://localhost:8000/api/contacts/customers/", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data.get('results', []))} customers")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Get products
    print("\n3️⃣ Testing GET /api/sales/products/")
    try:
        response = requests.get("http://localhost:8000/api/sales/products/", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data.get('results', []))} products")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 4: Get sales taxes
    print("\n4️⃣ Testing GET /api/sales-tax/")
    try:
        response = requests.get("http://localhost:8000/api/sales-tax/", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {len(data)} sales taxes")
        else:
            print(f"   ❌ Error: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    test_simple_api()
