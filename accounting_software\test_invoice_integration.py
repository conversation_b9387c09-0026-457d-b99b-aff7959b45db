#!/usr/bin/env python3
"""
Test script to verify invoice integration with inventory and pricing
"""
import requests
import json

def test_invoice_integration():
    """Test the complete invoice integration"""
    base_url = "http://localhost:8000/api/sales"
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("🧾 Testing Invoice Integration with Inventory & Pricing")
    print("=" * 60)
    
    # Step 1: Get available customers
    print("\n1️⃣ Getting available customers...")
    customers_response = requests.get("http://localhost:8000/api/contacts/customers/", headers=headers)
    
    if customers_response.status_code == 200:
        customers = customers_response.json()
        if customers.get('results'):
            customer_id = customers['results'][0]['id']
            customer_name = customers['results'][0]['display_name']
            print(f"✅ Using customer: {customer_name} (ID: {customer_id})")
        else:
            print("❌ No customers found")
            return
    else:
        print(f"❌ Failed to get customers: {customers_response.status_code}")
        return
    
    # Step 2: Get available warehouses
    print("\n2️⃣ Getting available warehouses...")
    warehouses_response = requests.get("http://localhost:8000/api/inventory/warehouses/", headers=headers)
    
    if warehouses_response.status_code == 200:
        warehouses = warehouses_response.json()
        if warehouses.get('results'):
            warehouse_id = warehouses['results'][0]['warehouse_id']
            warehouse_name = warehouses['results'][0]['name']
            print(f"✅ Using warehouse: {warehouse_name} (ID: {warehouse_id})")
        else:
            print("❌ No warehouses found")
            return
    else:
        print(f"❌ Failed to get warehouses: {warehouses_response.status_code}")
        return
    
    # Step 3: Get available products
    print("\n3️⃣ Getting available products...")
    products_response = requests.get(f"{base_url}/products/", headers=headers)
    
    if products_response.status_code == 200:
        products = products_response.json()
        if products.get('results'):
            # Get first few products for testing
            test_products = products['results'][:2]
            print(f"✅ Found {len(test_products)} products for testing:")
            for product in test_products:
                print(f"   - {product['name']} (ID: {product['id']}, Price: ${product['unit_price']})")
        else:
            print("❌ No products found")
            return
    else:
        print(f"❌ Failed to get products: {products_response.status_code}")
        return
    
    # Step 4: Check inventory availability
    print("\n4️⃣ Checking inventory availability...")
    for product in test_products:
        inventory_url = f"http://localhost:8000/api/inventory/inventory/?product_id={product['id']}&warehouse_id={warehouse_id}"
        inventory_response = requests.get(inventory_url, headers=headers)
        
        if inventory_response.status_code == 200:
            inventory_data = inventory_response.json()
            if inventory_data.get('results'):
                inventory = inventory_data['results'][0]
                available = inventory['quantity_on_hand'] - inventory['quantity_reserved']
                print(f"   📦 {product['name']}: {available} units available")
            else:
                print(f"   📦 {product['name']}: No inventory record found")
        else:
            print(f"   ❌ Failed to check inventory for {product['name']}")
    
    # Step 5: Create invoice with inventory validation
    print("\n5️⃣ Creating invoice with inventory validation...")
    
    invoice_data = {
        "customer": customer_id,
        "warehouse": warehouse_id,
        "invoice_date": "2024-01-15",
        "due_date": "2024-02-14",
        "terms": "Net 30",
        "notes": "Test invoice with inventory integration",
        "line_items": [
            {
                "product_id": test_products[0]['id'],
                "quantity": 2,
                "description": f"Test item - {test_products[0]['name']}"
            },
            {
                "product_id": test_products[1]['id'],
                "quantity": 1,
                "description": f"Test item - {test_products[1]['name']}"
            }
        ],
        "auto_apply_pricing": True,
        "validate_inventory": True,
        "issue_inventory": False  # Don't issue inventory yet
    }
    
    invoice_response = requests.post(f"{base_url}/invoices/", json=invoice_data, headers=headers)
    
    if invoice_response.status_code == 201:
        invoice = invoice_response.json()
        invoice_id = invoice['id']
        print(f"✅ Invoice created successfully!")
        print(f"   Invoice ID: {invoice_id}")
        print(f"   Invoice Number: {invoice.get('invoice_number', 'N/A')}")
        print(f"   Total Amount: ${invoice.get('total_amount', 0)}")
    else:
        print(f"❌ Failed to create invoice: {invoice_response.status_code}")
        print(f"   Error: {invoice_response.text}")
        return
    
    # Step 6: Check inventory status for the invoice
    print("\n6️⃣ Checking invoice inventory status...")
    inventory_status_response = requests.get(f"{base_url}/invoices/{invoice_id}/inventory_status/", headers=headers)
    
    if inventory_status_response.status_code == 200:
        inventory_status = inventory_status_response.json()
        print(f"✅ Inventory status retrieved:")
        print(f"   Overall Status: {inventory_status.get('overall_status', 'N/A')}")
        print(f"   Inventory Issued: {inventory_status.get('inventory_issued', False)}")
        
        for item in inventory_status.get('line_items', []):
            print(f"   📦 {item['product_name']}: {item['available_quantity']} available, {item['requested_quantity']} requested")
    else:
        print(f"❌ Failed to get inventory status: {inventory_status_response.status_code}")
    
    # Step 7: Get pricing details
    print("\n7️⃣ Getting pricing details...")
    pricing_response = requests.get(f"{base_url}/invoices/{invoice_id}/pricing_details/", headers=headers)
    
    if pricing_response.status_code == 200:
        pricing_data = pricing_response.json()
        print(f"✅ Pricing details retrieved:")
        
        customer_pricing = pricing_data.get('customer_pricing', {})
        print(f"   Customer: {customer_pricing.get('customer_name', 'N/A')}")
        print(f"   Price List: {customer_pricing.get('price_list', {}).get('name', 'Default')}")
        
        totals = pricing_data.get('totals', {})
        print(f"   Subtotal: ${totals.get('subtotal', 0)}")
        print(f"   Total Discount: ${totals.get('total_discount', 0)}")
    else:
        print(f"❌ Failed to get pricing details: {pricing_response.status_code}")
    
    # Step 8: Issue inventory (optional)
    print("\n8️⃣ Testing inventory issuing...")
    issue_response = requests.post(f"{base_url}/invoices/{invoice_id}/issue_inventory/", headers=headers)
    
    if issue_response.status_code == 200:
        issue_result = issue_response.json()
        print(f"✅ Inventory issued successfully!")
        print(f"   Details: {issue_result.get('message', 'N/A')}")
    else:
        print(f"❌ Failed to issue inventory: {issue_response.status_code}")
        print(f"   Error: {issue_response.text}")
    
    print("\n🎉 Invoice integration test completed!")

if __name__ == "__main__":
    test_invoice_integration()
